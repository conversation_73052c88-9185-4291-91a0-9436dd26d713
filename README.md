# Website Login Screenshot Tool

A Python script that automates logging into websites and taking full-page screenshots using Selenium WebDriver.

## Features

- 🔐 Automated website login with username/password
- 📸 Full-page screenshot capture
- 🎛️ Configurable CSS selectors for different websites
- ⚡ Headless mode support for server environments
- 🛡️ Robust error handling and timeouts
- 📁 Organized screenshot storage with timestamps

## Prerequisites

- Python 3.7 or higher
- Google Chrome browser installed
- ChromeDriver (will be managed automatically with webdriver-manager)

## Installation

1. **Clone or download the script files**

2. **Install required dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

3. **ChromeDriver Setup:**
   The script uses `webdriver-manager` to automatically download and manage ChromeDriver, so no manual setup is required.

## Usage

### Basic Usage

1. **Edit the configuration in `website_login_screenshot.py`:**
   ```python
   # Update these values in the main() function
   WEBSITE_URL = "https://your-website.com/login"
   USERNAME = "your_username"
   PASSWORD = "your_password"
   ```

2. **Adjust CSS selectors if needed:**
   ```python
   USERNAME_SELECTOR = "input[name='username']"  # Adjust for your site
   PASSWORD_SELECTOR = "input[name='password']"  # Adjust for your site
   LOGIN_BUTTON_SELECTOR = "input[type='submit']"  # Adjust for your site
   ```

3. **Run the script:**
   ```bash
   python website_login_screenshot.py
   ```

### Advanced Usage

You can also use the script as a module:

```python
from website_login_screenshot import WebsiteLoginScreenshot

# Initialize
login_tool = WebsiteLoginScreenshot(headless=True)  # Run in background

# Login and screenshot
success = login_tool.login_to_website(
    url="https://example.com/login",
    username="your_username",
    password="your_password",
    username_selector="#email",  # Custom selector
    password_selector="#password",
    login_button_selector="button.login-btn"
)

if success:
    screenshot_path = login_tool.take_full_page_screenshot()
    print(f"Screenshot saved: {screenshot_path}")

login_tool.close()
```

## Configuration Options

### CSS Selectors

Common CSS selector patterns for different websites:

| Element Type | Common Selectors |
|-------------|------------------|
| Username field | `input[name='username']`, `#username`, `#email`, `.username-field` |
| Password field | `input[name='password']`, `#password`, `.password-field` |
| Login button | `input[type='submit']`, `button[type='submit']`, `#login-btn`, `.login-button` |

### Browser Options

- **Headless mode**: Set `headless=True` for server environments
- **Window size**: Adjust `window_size=(width, height)` for different resolutions
- **Timeout**: Modify `wait_timeout` parameter for slower websites

## Output

Screenshots are saved in the `screenshots/` directory with timestamps:
```
screenshots/
├── screenshot_20241201_143022.png
├── screenshot_20241201_143155.png
└── ...
```

## Troubleshooting

### Common Issues

1. **ChromeDriver not found:**
   - The script uses webdriver-manager to handle this automatically
   - Ensure you have Chrome browser installed

2. **Login elements not found:**
   - Inspect the website's HTML to find correct CSS selectors
   - Use browser developer tools (F12) to identify form elements
   - Try alternative selectors like ID or class names

3. **Timeout errors:**
   - Increase the `wait_timeout` parameter
   - Check if the website has loading delays or CAPTCHA

4. **Screenshot issues:**
   - Some websites may block automated screenshots
   - Try running in non-headless mode first for debugging

### Finding CSS Selectors

1. Open the login page in Chrome
2. Right-click on the username field → "Inspect"
3. Copy the selector:
   - Right-click on the highlighted HTML → "Copy" → "Copy selector"
4. Repeat for password field and login button

## Security Notes

- ⚠️ **Never commit credentials to version control**
- Consider using environment variables for sensitive data:
  ```python
  import os
  USERNAME = os.getenv('WEBSITE_USERNAME')
  PASSWORD = os.getenv('WEBSITE_PASSWORD')
  ```
- Use this tool responsibly and respect website terms of service

## Example Websites

The script works with most standard login forms. Here are some common selector patterns:

```python
# Generic form
USERNAME_SELECTOR = "input[name='username']"
PASSWORD_SELECTOR = "input[name='password']"
LOGIN_BUTTON_SELECTOR = "input[type='submit']"

# Bootstrap-style forms
USERNAME_SELECTOR = "#inputEmail"
PASSWORD_SELECTOR = "#inputPassword"
LOGIN_BUTTON_SELECTOR = "button.btn-primary"

# Custom class names
USERNAME_SELECTOR = ".form-control[type='email']"
PASSWORD_SELECTOR = ".form-control[type='password']"
LOGIN_BUTTON_SELECTOR = ".btn-login"
```

## License

This project is provided as-is for educational and automation purposes. Use responsibly and in accordance with website terms of service. 