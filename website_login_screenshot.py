#!/usr/bin/env python3
"""
Website Login and Screenshot Script

This script automates the process of logging into a website,
entering credentials, and taking a full page screenshot.
"""

import time
import os
import re
from datetime import datetime
from urllib.parse import urlparse
import pandas as pd
import csv
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from PIL import Image


class WebsiteLoginScreenshot:
    def __init__(self, headless=False, window_size=(1920, 1080)):
        """
        Initialize the WebsiteLoginScreenshot class
        
        Args:
            headless (bool): Run browser in headless mode
            window_size (tuple): Browser window size (width, height)
        """
        self.driver = None
        self.headless = headless
        self.window_size = window_size
        self.setup_driver()
    
    def setup_driver(self):
        """Setup Chrome WebDriver with options"""
        chrome_options = Options()
        
        if self.headless:
            chrome_options.add_argument("--headless")
        
        # Add other useful options
        chrome_options.add_argument(f"--window-size={self.window_size[0]},{self.window_size[1]}")
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--disable-gpu")
        chrome_options.add_argument("--disable-extensions")
        chrome_options.add_argument("--disable-web-security")
        chrome_options.add_argument("--allow-running-insecure-content")
        
        try:
            # Try to use ChromeDriver from PATH
            self.driver = webdriver.Chrome(options=chrome_options)
            print("✓ Chrome WebDriver initialized successfully")
        except Exception as e:
            print(f"✗ Error initializing Chrome WebDriver: {e}")
            print("Make sure ChromeDriver is installed and in your PATH")
            raise
    
    def login_to_website(self, url, username, password, 
                        username_selector="input[name='username']",
                        password_selector="input[name='password']",
                        login_button_selector="input[type='submit']",
                        wait_timeout=10):
        """
        Login to a website with provided credentials
        
        Args:
            url (str): Website URL to login
            username (str): Username/email for login
            password (str): Password for login
            username_selector (str): CSS selector for username field
            password_selector (str): CSS selector for password field
            login_button_selector (str): CSS selector for login button
            wait_timeout (int): Maximum time to wait for elements
        
        Returns:
            dict: Dictionary containing login status and details
        """
        start_time = time.time()
        result = {
            'connection_status': 'Unknown',
            'login_status': 'Unknown',
            'success': False,
            'error_message': '',
            'response_time': 0,
            'final_url': '',
            'page_title': '',
            'elements_found': {
                'username_field': False,
                'password_field': False,
                'login_button': False
            }
        }
        
        try:
            print(f"🌐 Navigating to: {url}")
            self.driver.get(url)
            result['connection_status'] = 'Connected'
            result['response_time'] = round(time.time() - start_time, 2)
            
            # Wait for page to load
            WebDriverWait(self.driver, wait_timeout).until(
                EC.presence_of_element_located((By.TAG_NAME, "body"))
            )
            
            # Get page title and final URL
            result['page_title'] = self.driver.title
            result['final_url'] = self.driver.current_url
            
            print("⏳ Waiting for login form elements...")
            
            # Wait for and find username field
            try:
                username_field = WebDriverWait(self.driver, wait_timeout).until(
                    EC.presence_of_element_located((By.CSS_SELECTOR, username_selector))
                )
                result['elements_found']['username_field'] = True
            except TimeoutException:
                result['error_message'] = f"Username field not found: {username_selector}"
                result['login_status'] = 'Element Not Found'
                return result
            
            # Wait for and find password field
            try:
                password_field = WebDriverWait(self.driver, wait_timeout).until(
                    EC.presence_of_element_located((By.CSS_SELECTOR, password_selector))
                )
                result['elements_found']['password_field'] = True
            except TimeoutException:
                result['error_message'] = f"Password field not found: {password_selector}"
                result['login_status'] = 'Element Not Found'
                return result
            
            # Wait for and find login button
            try:
                login_button = WebDriverWait(self.driver, wait_timeout).until(
                    EC.element_to_be_clickable((By.CSS_SELECTOR, login_button_selector))
                )
                result['elements_found']['login_button'] = True
            except TimeoutException:
                result['error_message'] = f"Login button not found: {login_button_selector}"
                result['login_status'] = 'Element Not Found'
                return result
            
            print("📝 Entering credentials...")
            
            # Clear and enter username
            username_field.clear()
            username_field.send_keys(username)
            
            # Clear and enter password
            password_field.clear()
            password_field.send_keys(password)
            
            # Small delay before clicking login
            time.sleep(1)
            
            print("🔐 Clicking login button...")
            login_button.click()
            
            # Wait for login to process and page to redirect/load
            print("⏳ Waiting for page to load after login...")
            time.sleep(5)
            
            # Wait for the page to be in a ready state
            WebDriverWait(self.driver, wait_timeout).until(
                lambda driver: driver.execute_script("return document.readyState") == "complete"
            )
            
            # Update final URL and title after login
            result['final_url'] = self.driver.current_url
            result['page_title'] = self.driver.title
            result['response_time'] = round(time.time() - start_time, 2)
            
            # Check if login was successful by looking for common success indicators
            # This is a basic check - you might want to customize this per website
            current_url = self.driver.current_url.lower()
            page_title = self.driver.title.lower()
            

            
            # Simplified login success determination - assume success if we got past the login form
            # and the page has loaded without obvious error indicators
            error_indicators = ['error', 'invalid', 'failed', 'incorrect', 'denied']
            
            # Check if we're still on the same login page (potential failure)
            if current_url == url or 'login' in current_url.lower():
                # Check page content for error messages
                try:
                    page_source = self.driver.page_source.lower()
                    if any(indicator in page_source for indicator in error_indicators):
                        result['login_status'] = 'Login Failed'
                        result['error_message'] = 'Login appears to have failed - error indicators found'
                        result['success'] = False
                    else:
                        result['login_status'] = 'Success'
                        result['success'] = True
                except:
                    result['login_status'] = 'Success'
                    result['success'] = True
            else:
                # URL changed after login - assume success
                result['login_status'] = 'Success'
                result['success'] = True
            
            print("✓ Login attempt completed and page loaded")
            return result
            
        except TimeoutException as e:
            print(f"✗ Timeout waiting for elements: {e}")
            result['connection_status'] = 'Timeout'
            result['login_status'] = 'Timeout'
            result['error_message'] = str(e)
            result['response_time'] = round(time.time() - start_time, 2)
            return result
        except NoSuchElementException as e:
            print(f"✗ Could not find required elements: {e}")
            result['connection_status'] = 'Connected'
            result['login_status'] = 'Element Not Found'
            result['error_message'] = str(e)
            result['response_time'] = round(time.time() - start_time, 2)
            return result
        except Exception as e:
            print(f"✗ Error during login: {e}")
            result['connection_status'] = 'Error'
            result['login_status'] = 'Error'
            result['error_message'] = str(e)
            result['response_time'] = round(time.time() - start_time, 2)
            return result
    
    def wait_for_page_load(self, timeout=30):
        """
        Wait for the page to be completely loaded including all resources
        
        Args:
            timeout (int): Maximum time to wait for page load
        """
        try:
            print("⏳ Waiting for complete page load...")
            
            # Wait for document ready state
            WebDriverWait(self.driver, timeout).until(
                lambda driver: driver.execute_script("return document.readyState") == "complete"
            )
            
            # Wait for jQuery to be loaded and ready (if present)
            try:
                WebDriverWait(self.driver, 5).until(
                    lambda driver: driver.execute_script("return typeof jQuery !== 'undefined' && jQuery.active == 0")
                )
                print("✓ jQuery operations completed")
            except:
                print("ℹ️ jQuery not detected or not using jQuery")
            
            # Wait for any pending network requests to complete
            time.sleep(2)
            
            print("✓ Page load completed")
            
        except Exception as e:
            print(f"⚠️ Page load wait encountered issue: {e}")
    
    def wait_for_appointment_data(self, timeout=60):
        """
        Wait for appointment data to load, handling "No Records Found" scenarios
        
        Args:
            timeout (int): Maximum time to wait for appointment data
            
        Returns:
            dict: Dictionary containing data loading status and refresh attempts
        """
        try:
            print("⏳ Waiting for appointment data to load...")
            
            start_time = time.time()
            max_wait_time = timeout
            check_interval = 3  # Check every 3 seconds
            refresh_attempted = False  # Track if we've tried a second refresh
            
            while time.time() - start_time < max_wait_time:
                
                # Check if "No Records Found" is displayed
                no_records_elements = self.driver.find_elements(By.XPATH, 
                    "//*[contains(text(), 'No Records Found') or contains(text(), 'No records found') or contains(text(), 'no records found')]")
                
                if no_records_elements:
                    # Check if we're on the appointments page (has the appointments tab/header)
                    appointments_page_indicators = self.driver.find_elements(By.XPATH, 
                        "//*[contains(text(), 'Appointments') or contains(@class, 'appointment') or contains(@id, 'appointment')]")
                    
                    if appointments_page_indicators:
                        # We're on the appointments page with "No Records Found"
                        elapsed_time = time.time() - start_time
                        
                        # Try a second refresh after 5 seconds if we haven't tried it yet
                        if not refresh_attempted and elapsed_time >= 3:
                            print(f"🔄 'No Records Found' after {int(elapsed_time)}s - attempting second refresh...")
                            refresh_success = self.click_refresh_button(timeout=5)
                            refresh_attempted = True
                            if refresh_success:
                                print("✓ Second refresh completed, continuing to wait for data...")
                                time.sleep(check_interval)
                                continue
                            else:
                                print("⚠️ Second refresh failed, continuing to wait...")
                        
                        # Check if this is a temporary loading state or actual no data
                        if elapsed_time < 25:  # Extended wait time to account for second refresh
                            print(f"⏳ On appointments page with 'No Records Found', waiting... ({int(elapsed_time)}s)")
                            time.sleep(check_interval)
                            continue
                        else:
                            print("ℹ️ 'No Records Found' appears to be the final state - no appointments for this date/filter")
                            return {'data_loaded': False, 'second_refresh_attempted': refresh_attempted}
                    else:
                        print(f"⏳ 'No Records Found' detected, waiting for data to load... ({int(time.time() - start_time)}s)")
                        time.sleep(check_interval)
                        continue
                
                # Check for appointment data indicators - be more specific
                data_found = False
                
                # First, check if pagination controls exist (strong indicator of data)
                try:
                    pagination = self.driver.find_elements(By.CSS_SELECTOR, '#AppointmentTheme_ThemeControlID_pagingcontrols')
                    if pagination and pagination[0].is_displayed():
                        print(f"✓ Appointment data loaded! Found pagination controls")
                        data_found = True
                except:
                    pass
                
                # If no pagination, look for actual appointment table rows with meaningful data
                if not data_found:
                    try:
                        # Look for table rows that contain actual appointment data (not just headers)
                        appointment_rows = self.driver.find_elements(By.CSS_SELECTOR, 'table tbody tr')
                        meaningful_rows = []
                        
                        for row in appointment_rows:
                            if row.is_displayed():
                                # Check if row contains actual appointment data
                                cells = row.find_elements(By.TAG_NAME, 'td')
                                if len(cells) >= 3:  # Should have multiple columns for appointment data
                                    # Check if cells contain meaningful text (not just empty or whitespace)
                                    cell_texts = [cell.text.strip() for cell in cells[:5]]  # Check first 5 cells
                                    non_empty_cells = [text for text in cell_texts if text and len(text) > 1]
                                    
                                    if len(non_empty_cells) >= 2:  # At least 2 cells with meaningful data
                                        meaningful_rows.append(row)
                        
                        if len(meaningful_rows) >= 1:  # Found at least 1 row with meaningful data
                            print(f"✓ Appointment data loaded! Found {len(meaningful_rows)} appointment rows")
                            data_found = True
                    except Exception as e:
                        print(f"⚠️ Error checking appointment rows: {e}")
                
                # Additional check for specific appointment elements
                if not data_found:
                    try:
                        # Look for elements that specifically indicate appointment data
                        appointment_elements = self.driver.find_elements(By.CSS_SELECTOR, 
                            '[id*="appointment"], [class*="appointment"], [id*="AppointmentTheme"] .appointment')
                        visible_appointments = [elem for elem in appointment_elements if elem.is_displayed() and elem.text.strip()]
                        
                        if visible_appointments:
                            print(f"✓ Appointment data loaded! Found {len(visible_appointments)} appointment elements")
                            data_found = True
                    except:
                        pass
                
                if data_found:
                    # Give a bit more time for all data to fully load
                    time.sleep(3)
                    print("✓ Appointment data fully loaded")
                    return {'data_loaded': True, 'second_refresh_attempted': refresh_attempted}
                
                print(f"⏳ Still waiting for appointment data... ({int(time.time() - start_time)}s)")
                time.sleep(check_interval)
            
            # Timeout reached
            print(f"⚠️ Timeout reached ({timeout}s) - checking final state")
            
            # Final strict check for actual appointment data
            try:
                # Check if "No Records Found" is still showing
                no_records_still_showing = self.driver.find_elements(By.XPATH, 
                    "//*[contains(text(), 'No Records Found') or contains(text(), 'No records found')]")
                
                if no_records_still_showing:
                    print("❌ 'No Records Found' still displayed - no appointment data available")
                    return {'data_loaded': False, 'second_refresh_attempted': refresh_attempted}
                
                # Check for pagination (strongest indicator)
                pagination = self.driver.find_elements(By.CSS_SELECTOR, '#AppointmentTheme_ThemeControlID_pagingcontrols')
                if pagination and pagination[0].is_displayed():
                    print("✓ Final check: Pagination found - data is available")
                    return {'data_loaded': True, 'second_refresh_attempted': refresh_attempted}
                
                # Check for meaningful table rows
                appointment_rows = self.driver.find_elements(By.CSS_SELECTOR, 'table tbody tr')
                meaningful_rows = 0
                for row in appointment_rows:
                    if row.is_displayed():
                        cells = row.find_elements(By.TAG_NAME, 'td')
                        if len(cells) >= 3:
                            cell_texts = [cell.text.strip() for cell in cells[:3]]
                            non_empty_cells = [text for text in cell_texts if text and len(text) > 1]
                            if len(non_empty_cells) >= 2:
                                meaningful_rows += 1
                
                if meaningful_rows >= 1:
                    print(f"✓ Final check: Found {meaningful_rows} rows with appointment data")
                    return {'data_loaded': True, 'second_refresh_attempted': refresh_attempted}
                else:
                    print("❌ Final check: No meaningful appointment data found")
                    return {'data_loaded': False, 'second_refresh_attempted': refresh_attempted}
                    
            except Exception as e:
                print(f"⚠️ Error in final data check: {e}")
                return {'data_loaded': False, 'second_refresh_attempted': refresh_attempted}
                
        except Exception as e:
            print(f"⚠️ Error waiting for appointment data: {e}")
            return {'data_loaded': False, 'second_refresh_attempted': False}
    
    def combine_screenshots(self, screenshot_files, output_path):
        """
        Combine multiple screenshot files into one full-page image
        
        Args:
            screenshot_files (list): List of screenshot file paths
            output_path (str): Path for the combined output image
        """
        try:
            if not screenshot_files:
                print("❌ No screenshots to combine")
                return False
            
            # Open all images
            images = []
            for file_path in screenshot_files:
                try:
                    img = Image.open(file_path)
                    images.append(img)
                except Exception as e:
                    print(f"⚠️ Could not open {file_path}: {e}")
            
            if not images:
                print("❌ No valid images to combine")
                return False
            
            print(f"🔧 Combining {len(images)} screenshots...")
            
            # Get dimensions
            max_width = max(img.width for img in images)
            
            # Calculate total height more carefully
            # First image: full height
            # Subsequent images: subtract overlap amount
            overlap_pixels = 100  # This should match the overlap used in scrolling
            total_height = images[0].height
            for i in range(1, len(images)):
                total_height += (images[i].height - overlap_pixels)
            
            print(f"📏 Creating combined image: {max_width} x {total_height}")
            
            # Create new image with combined dimensions
            combined_image = Image.new('RGB', (max_width, total_height), 'white')
            
            # Paste images vertically with proper overlap handling
            current_y = 0
            for i, img in enumerate(images):
                if i == 0:
                    # First image: paste completely
                    combined_image.paste(img, (0, current_y))
                    current_y += img.height
                    print(f"📸 Pasted section {i+1}: full image at y=0")
                else:
                    # Subsequent images: remove overlap from top
                    crop_box = (0, overlap_pixels, img.width, img.height)
                    img_cropped = img.crop(crop_box)
                    paste_y = current_y - overlap_pixels
                    combined_image.paste(img_cropped, (0, paste_y))
                    current_y = paste_y + img_cropped.height
                    print(f"📸 Pasted section {i+1}: cropped {overlap_pixels}px from top, pasted at y={paste_y}")
            
            # Save the combined image
            combined_image.save(output_path, 'PNG', optimize=True, quality=95)
            
            # Close all images to free memory
            for img in images:
                img.close()
            combined_image.close()
            
            print(f"✅ Successfully combined {len(images)} screenshots")
            print(f"📏 Final image dimensions: {max_width} x {total_height}")
            return True
            
        except Exception as e:
            print(f"❌ Error combining screenshots: {e}")
            return False
    
    def generate_filename_from_url(self, url):
        """
        Generate a filename based on the website URL
        
        Args:
            url (str): Website URL
            
        Returns:
            str: Generated filename
        """
        try:
            # Parse the URL to get the domain
            parsed_url = urlparse(url)
            domain = parsed_url.netloc
            
            # Remove common prefixes and clean up
            domain = re.sub(r'^www\.', '', domain)  # Remove www.
            domain = re.sub(r':\d+', '', domain)   # Remove port numbers
            
            # Replace dots and special characters with underscores
            clean_domain = re.sub(r'[^\w\-]', '_', domain)
            
            # Generate timestamp
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            
            # Create filename
            filename = f"{clean_domain}_{timestamp}.png"
            
            return filename
            
        except Exception as e:
            print(f"⚠️ Error generating filename from URL: {e}")
            # Fallback to default naming
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            return f"screenshot_{timestamp}.png"
    
    def count_appointment_pages(self):
        """
        Count the total number of appointment pages from pagination
        
        Returns:
            dict: Dictionary containing page count information
        """
        try:
            page_info = {
                'total_appointment_pages': 0,
                'current_page': 0,
                'pagination_detected': False,
                'page_numbers': []
            }
            
            # Look for the specific pagination controls from your HTML
            try:
                # Check if pagination container exists
                pagination_container = self.driver.find_element(By.CSS_SELECTOR, '#AppointmentTheme_ThemeControlID_pagingcontrols')
                if pagination_container:
                    page_info['pagination_detected'] = True
                    print("📄 Pagination controls found")
                    
                    # Get all page number links with data-commandargument
                    page_links = self.driver.find_elements(By.CSS_SELECTOR, 
                        '#AppointmentTheme_ThemeControlID_pagingcontrols a[data-commandargument]')
                    
                    if page_links:
                        page_numbers = []
                        for link in page_links:
                            try:
                                page_num = int(link.get_attribute('data-commandargument'))
                                page_numbers.append(page_num)
                                print(f"📋 Found page: {page_num}")
                            except (ValueError, TypeError):
                                continue
                        
                        if page_numbers:
                            page_info['page_numbers'] = sorted(page_numbers)
                            page_info['total_appointment_pages'] = max(page_numbers)
                            print(f"📊 Total appointment pages found: {page_info['total_appointment_pages']}")
                    
                    # Find current active page (has class="active")
                    try:
                        active_page_element = self.driver.find_element(By.CSS_SELECTOR, 
                            '#AppointmentTheme_ThemeControlID_pagingcontrols .active a[data-commandargument]')
                        page_info['current_page'] = int(active_page_element.get_attribute('data-commandargument'))
                        print(f"📍 Currently on page: {page_info['current_page']}")
                    except:
                        # If no active page found, assume page 1
                        page_info['current_page'] = 1
                        print("📍 Current page not detected, assuming page 1")
                        
            except:
                # No pagination found
                print("📄 No pagination controls found - single page")
                page_info['total_appointment_pages'] = 1
                page_info['current_page'] = 1
            
            return page_info
            
        except Exception as e:
            print(f"⚠️ Error counting appointment pages: {e}")
            return {
                'total_appointment_pages': 0,
                'current_page': 0,
                'pagination_detected': False,
                'page_numbers': []
            }
    
    def take_full_page_screenshot(self, filename=None, output_dir="screenshots", url=None):
        """
        Take a screenshot of the entire page
        
        Args:
            filename (str): Custom filename for screenshot
            output_dir (str): Directory to save screenshots
            url (str): Website URL (used for auto-generating filename)
        
        Returns:
            str: Path to saved screenshot file
        """
        try:
            # Create output directory if it doesn't exist
            os.makedirs(output_dir, exist_ok=True)
            
            # Generate filename if not provided
            if not filename:
                if url:
                    filename = self.generate_filename_from_url(url)
                else:
                    # Try to get URL from current page
                    try:
                        current_url = self.driver.current_url
                        filename = self.generate_filename_from_url(current_url)
                    except:
                        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                        filename = f"screenshot_{timestamp}.png"
            
            # Ensure filename has .png extension
            if not filename.endswith('.png'):
                filename += '.png'
            
            filepath = os.path.join(output_dir, filename)
            print(f"📁 Screenshot will be saved as: {filename}")
            
            print("📸 Taking full page screenshot using scroll method...")
            
            # Get viewport dimensions
            viewport_width = self.driver.execute_script("return window.innerWidth")
            viewport_height = self.driver.execute_script("return window.innerHeight")
            
            # Get total page dimensions
            total_width = self.driver.execute_script("return Math.max(document.body.scrollWidth, document.body.offsetWidth, document.documentElement.clientWidth, document.documentElement.scrollWidth, document.documentElement.offsetWidth)")
            total_height = self.driver.execute_script("return Math.max(document.body.scrollHeight, document.body.offsetHeight, document.documentElement.clientHeight, document.documentElement.scrollHeight, document.documentElement.offsetHeight)")
            
            print(f"📏 Viewport: {viewport_width} x {viewport_height}")
            print(f"📏 Total page: {total_width} x {total_height}")
            
            # Set a reasonable window width but keep original height for scrolling
            self.driver.set_window_size(max(viewport_width, total_width), viewport_height)
            time.sleep(2)
            
            # Scroll to top first
            self.driver.execute_script("window.scrollTo(0, 0);")
            time.sleep(1)
            
            # Calculate how many screenshots we need to take
            overlap_pixels = 100  # Overlap to avoid missing content
            scroll_height = viewport_height - overlap_pixels
            screenshots = []
            current_scroll = 0
            
            print("📸 Taking multiple screenshots to capture full page...")
            print(f"📏 Scroll height per section: {scroll_height}, overlap: {overlap_pixels}px")
            
            while current_scroll < total_height:
                # Scroll to current position
                self.driver.execute_script(f"window.scrollTo(0, {current_scroll});")
                time.sleep(1.5)
                
                # Wait for content to load at this scroll position
                WebDriverWait(self.driver, 10).until(
                    lambda driver: driver.execute_script("return document.readyState") == "complete"
                )
                time.sleep(1)
                
                # Take screenshot of current viewport
                temp_filepath = filepath.replace('.png', f'_temp_{len(screenshots)}.png')
                self.driver.save_screenshot(temp_filepath)
                screenshots.append(temp_filepath)
                
                print(f"📸 Captured section {len(screenshots)} at scroll position {current_scroll}")
                
                # Check if this is likely the last screenshot needed
                remaining_height = total_height - current_scroll
                if remaining_height <= viewport_height:
                    print(f"📸 Remaining height ({remaining_height}px) fits in viewport, this should be the last section")
                    break
                
                # Move to next scroll position
                current_scroll += scroll_height
                
                # Safety check to avoid infinite loop
                if len(screenshots) > 25:
                    print("⚠️ Too many sections, stopping to avoid memory issues")
                    break
            
            # Ensure we capture the very bottom by taking one final screenshot at the bottom
            if current_scroll < total_height - viewport_height:
                final_scroll = max(0, total_height - viewport_height)
                print(f"📸 Taking final screenshot at bottom position: {final_scroll}")
                self.driver.execute_script(f"window.scrollTo(0, {final_scroll});")
                time.sleep(1.5)
                
                temp_filepath = filepath.replace('.png', f'_temp_{len(screenshots)}.png')
                self.driver.save_screenshot(temp_filepath)
                screenshots.append(temp_filepath)
                print(f"📸 Captured final section {len(screenshots)} at scroll position {final_scroll}")
            
            print(f"📸 Captured {len(screenshots)} sections, combining into full page screenshot...")
            
            # Combine all screenshots into one
            self.combine_screenshots(screenshots, filepath)
            
            # Clean up temporary files
            for temp_file in screenshots:
                try:
                    os.remove(temp_file)
                except:
                    pass
            
            print(f"✓ Screenshot saved: {filepath}")
            return filepath
            
        except Exception as e:
            print(f"✗ Error taking screenshot: {e}")
            return None
    
    def close(self):
        """Close the WebDriver"""
        if self.driver:
            self.driver.quit()
            print("�� Browser closed")

    def click_refresh_button(self, timeout=10):
        """
        Click the refresh button on the dashboard and wait for page to reload
        
        Args:
            timeout (int): Maximum time to wait for refresh button
            
        Returns:
            bool: True if refresh button was found and clicked, False otherwise
        """
        try:
            print("🔄 Looking for refresh button...")
            
            # Try to find the refresh button using the provided selector
            refresh_selectors = [
                '#btnRefresh',  # ID selector from the provided HTML
                '.refresh_btn',  # Class selector from the provided HTML
                'a[data-role="button"][title="Refresh "]',  # Attribute selector
                'a[id="btnRefresh"]',  # Alternative ID selector
                'img[src*="refresh.png"]',  # Image source selector
                'a:has(img[src*="refresh"])'  # Selector for link containing refresh image
            ]
            
            refresh_button = None
            for selector in refresh_selectors:
                try:
                    refresh_button = WebDriverWait(self.driver, timeout).until(
                        EC.element_to_be_clickable((By.CSS_SELECTOR, selector))
                    )
                    print(f"✓ Found refresh button using selector: {selector}")
                    break
                except TimeoutException:
                    continue
            
            if not refresh_button:
                print("⚠️ Refresh button not found, proceeding without refresh")
                return False
            
            print("🔄 Clicking refresh button...")
            
            # Scroll the refresh button into view if needed
            self.driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", refresh_button)
            time.sleep(1)
            
            # Click the refresh button
            refresh_button.click()
            print("✓ Refresh button clicked successfully")
            
            # Wait a moment for the refresh to start
            time.sleep(2)
            
            # Wait for the page to reload completely
            print("⏳ Waiting for page to reload after refresh...")
            WebDriverWait(self.driver, timeout).until(
                lambda driver: driver.execute_script("return document.readyState") == "complete"
            )
            
            # Additional wait for any dynamic content to load
            time.sleep(3)
            
            print("✓ Page refreshed and reloaded successfully")
            return True
            
        except TimeoutException:
            print("⚠️ Timeout waiting for refresh button")
            return False
        except Exception as e:
            print(f"⚠️ Error clicking refresh button: {e}")
            return False

    def handle_organization_selection(self, timeout=15):
        """
        Handle organization selection page if it appears after login
        
        Args:
            timeout (int): Maximum time to wait for organization selection elements
            
        Returns:
            dict: Dictionary containing organization selection status and details
        """
        try:
            print("🏢 Checking for organization selection page...")
            
            # Wait a moment for page to load after login
            time.sleep(2)
            
            # Check for organization selection indicators
            org_selection_indicators = [
                # Text-based indicators
                "//*[contains(text(), 'Please select Organization') or contains(text(), 'Organization Selection') or contains(text(), 'Select Organization')]",
                # Form-based indicators
                "//form[contains(@action, 'organization') or contains(@id, 'organization')]",
                # Button indicators
                "//button[contains(text(), 'Go') and ancestor::*[contains(text(), 'Organization')]]",
                "//input[@type='submit' and @value='Go' and ancestor::*[contains(text(), 'Organization')]]",
                # Radio button indicators for organization selection
                "//input[@type='radio' and contains(@name, 'organization')]",
                "//input[@type='radio' and ancestor::*[contains(text(), 'Organization')]]"
            ]
            
            organization_page_found = False
            for indicator in org_selection_indicators:
                try:
                    elements = self.driver.find_elements(By.XPATH, indicator)
                    if elements and any(elem.is_displayed() for elem in elements):
                        print(f"✓ Organization selection page detected using: {indicator}")
                        organization_page_found = True
                        break
                except:
                    continue
            
            if not organization_page_found:
                print("ℹ️ No organization selection page detected - proceeding to dashboard")
                return {
                    'organization_page_found': False,
                    'organization_selected': False,
                    'error_message': ''
                }
            
            print("🏢 Organization selection page found - attempting to select organization...")
            
            # Try to find and select the first available organization
            organization_selected = False
            
            # Strategy 1: Look for radio buttons and select the first one
            try:
                radio_buttons = self.driver.find_elements(By.XPATH, "//input[@type='radio']")
                visible_radios = [rb for rb in radio_buttons if rb.is_displayed()]
                
                if visible_radios:
                    # Select the first radio button (usually the default organization)
                    first_radio = visible_radios[0]
                    if not first_radio.is_selected():
                        print("🔘 Selecting first organization option...")
                        self.driver.execute_script("arguments[0].click();", first_radio)
                        time.sleep(1)
                    
                    # Look for and click the "Go" button
                    go_buttons = self.driver.find_elements(By.XPATH, 
                        "//button[contains(text(), 'Go')] | //input[@type='submit' and @value='Go'] | //input[@type='button' and @value='Go']")
                    
                    for button in go_buttons:
                        if button.is_displayed() and button.is_enabled():
                            print("▶️ Clicking 'Go' button to proceed...")
                            self.driver.execute_script("arguments[0].click();", button)
                            organization_selected = True
                            break
                    
                    if organization_selected:
                        print("✓ Organization selected and submitted")
                        # Wait for navigation to dashboard
                        time.sleep(3)
                        
                        # Wait for page to load completely
                        WebDriverWait(self.driver, timeout).until(
                            lambda driver: driver.execute_script("return document.readyState") == "complete"
                        )
                        time.sleep(2)
                        
                        return {
                            'organization_page_found': True,
                            'organization_selected': True,
                            'error_message': ''
                        }
            except Exception as e:
                print(f"⚠️ Error with radio button selection: {e}")
            
            # Strategy 2: Look for dropdown selection (alternative approach)
            if not organization_selected:
                try:
                    dropdowns = self.driver.find_elements(By.TAG_NAME, "select")
                    for dropdown in dropdowns:
                        if dropdown.is_displayed():
                            options = dropdown.find_elements(By.TAG_NAME, "option")
                            if len(options) > 1:  # More than just placeholder option
                                print("📋 Selecting from organization dropdown...")
                                # Select the first non-empty option
                                for option in options[1:]:  # Skip first option (usually placeholder)
                                    if option.text.strip():
                                        option.click()
                                        organization_selected = True
                                        break
                                break
                    
                    if organization_selected:
                        # Look for submit button after dropdown selection
                        submit_buttons = self.driver.find_elements(By.XPATH, 
                            "//button[@type='submit'] | //input[@type='submit'] | //button[contains(text(), 'Go')] | //button[contains(text(), 'Submit')]")
                        
                        for button in submit_buttons:
                            if button.is_displayed() and button.is_enabled():
                                print("▶️ Submitting organization selection...")
                                button.click()
                                time.sleep(3)
                                return {
                                    'organization_page_found': True,
                                    'organization_selected': True,
                                    'error_message': ''
                                }
                except Exception as e:
                    print(f"⚠️ Error with dropdown selection: {e}")
            
            # If we get here, we found the page but couldn't select an organization
            if not organization_selected:
                error_msg = "Organization selection page found but could not automatically select organization"
                print(f"❌ {error_msg}")
                return {
                    'organization_page_found': True,
                    'organization_selected': False,
                    'error_message': error_msg
                }
            
        except TimeoutException:
            print("⚠️ Timeout while handling organization selection")
            return {
                'organization_page_found': False,
                'organization_selected': False,
                'error_message': 'Timeout during organization selection'
            }
        except Exception as e:
            print(f"⚠️ Error handling organization selection: {e}")
            return {
                'organization_page_found': False,
                'organization_selected': False,
                'error_message': str(e)
            }


def read_websites_from_excel(excel_file):
    """
    Read website credentials from Excel file
    
    Args:
        excel_file (str): Path to Excel file
    
    Returns:
        list: List of dictionaries containing website information
    """
    try:
        print(f"📖 Reading websites from: {excel_file}")
        
        # Read Excel file
        df = pd.read_excel(excel_file)
        
        # Print column names for debugging
        print(f"📋 Available columns: {list(df.columns)}")
        
        # Convert to list of dictionaries
        websites = []
        for index, row in df.iterrows():
            # Try different possible column name variations
            website_data = {}
            
            # URL column (prioritize exact match from Cred.xlsx)
            url_columns = ['URL', 'website', 'url', 'Website', 'site', 'Site']
            for col in url_columns:
                if col in df.columns and pd.notna(row[col]):
                    website_data['url'] = str(row[col]).strip()
                    break
            
            # Username column (prioritize exact match from Cred.xlsx)
            username_columns = ['Username', 'username', 'user', 'User', 'email', 'Email']
            for col in username_columns:
                if col in df.columns and pd.notna(row[col]):
                    website_data['username'] = str(row[col]).strip()
                    break
            
            # Password column (prioritize exact match from Cred.xlsx)
            password_columns = ['Password', 'password', 'pass', 'Pass', 'pwd', 'Pwd']
            for col in password_columns:
                if col in df.columns and pd.notna(row[col]):
                    website_data['password'] = str(row[col]).strip()
                    break
            
            # Client column (optional - for reference)
            if 'Client' in df.columns and pd.notna(row['Client']):
                website_data['client'] = str(row['Client']).strip()
            
            # CSS selectors (optional columns)
            selector_columns = {
                'username_selector': ['username_selector', 'user_selector', 'Username_Selector'],
                'password_selector': ['password_selector', 'pass_selector', 'Password_Selector'],
                'login_button_selector': ['login_selector', 'button_selector', 'Login_Selector']
            }
            
            for key, possible_cols in selector_columns.items():
                for col in possible_cols:
                    if col in df.columns and pd.notna(row[col]):
                        website_data[key] = str(row[col]).strip()
                        break
            
            # Only add if we have at least URL, username, and password
            if all(key in website_data for key in ['url', 'username', 'password']):
                websites.append(website_data)
                client_info = f" ({website_data['client']})" if 'client' in website_data else ""
                print(f"✓ Added website: {website_data['url']}{client_info}")
            else:
                print(f"⚠️ Skipping row {index + 1}: Missing required fields (url, username, password)")
        
        print(f"📊 Found {len(websites)} valid websites to process")
        return websites
        
    except FileNotFoundError:
        print(f"❌ Excel file not found: {excel_file}")
        return []
    except Exception as e:
        print(f"❌ Error reading Excel file: {e}")
        return []


def create_csv_report(results, output_file="website_status_report.csv"):
    """
    Create a CSV report with website status information
    
    Args:
        results (list): List of result dictionaries
        output_file (str): Output CSV filename
    """
    try:
        fieldnames = [
            'timestamp',
            'client',
            'website_url',
            'connection_status',
            'login_status',
            'response_time_seconds',
            'page_title',
            'final_url',
            'username_field_found',
            'password_field_found',
            'login_button_found',
            'total_appointment_pages',
            'current_page',
            'pagination_detected',
            'data_loaded_successfully',
            'refresh_button_clicked',
            'second_refresh_attempted',
            'organization_page_found',
            'organization_selected',
            'screenshot_path',
            'error_message',
            'username_selector',
            'password_selector',
            'login_button_selector'
        ]
        
        with open(output_file, 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()
            
            for result in results:
                # Flatten the nested elements_found dictionary
                row = {
                    'timestamp': result.get('timestamp', ''),
                    'client': result.get('client', ''),
                    'website_url': result.get('website', ''),
                    'connection_status': result.get('connection_status', ''),
                    'login_status': result.get('login_status', ''),
                    'response_time_seconds': result.get('response_time', ''),
                    'page_title': result.get('page_title', ''),
                    'final_url': result.get('final_url', ''),
                    'username_field_found': result.get('elements_found', {}).get('username_field', False),
                    'password_field_found': result.get('elements_found', {}).get('password_field', False),
                    'login_button_found': result.get('elements_found', {}).get('login_button', False),
                    'total_appointment_pages': result.get('page_info', {}).get('total_appointment_pages', 0),
                    'current_page': result.get('page_info', {}).get('current_page', 0),
                    'pagination_detected': result.get('page_info', {}).get('pagination_detected', False),
                    'data_loaded_successfully': result.get('data_loaded', False),
                    'refresh_button_clicked': result.get('refresh_clicked', False),
                    'second_refresh_attempted': result.get('second_refresh_attempted', False),
                    'organization_page_found': result.get('organization_page_found', False),
                    'organization_selected': result.get('organization_selected', False),
                    'screenshot_path': result.get('screenshot', ''),
                    'error_message': result.get('error_message', ''),
                    'username_selector': result.get('username_selector', ''),
                    'password_selector': result.get('password_selector', ''),
                    'login_button_selector': result.get('login_button_selector', '')
                }
                writer.writerow(row)
        
        print(f"📊 CSV report created: {output_file}")
        return output_file
        
    except Exception as e:
        print(f"❌ Error creating CSV report: {e}")
        return None


def process_multiple_websites(excel_file, headless=False):
    """
    Process multiple websites from Excel file
    
    Args:
        excel_file (str): Path to Excel file
        headless (bool): Run browser in headless mode
    """
    # Read websites from Excel
    websites = read_websites_from_excel(excel_file)
    
    if not websites:
        print("❌ No websites to process")
        return
    
    # Initialize results tracking
    results = []
    successful = 0
    failed = 0
    
    print(f"\n🚀 Starting to process {len(websites)} websites...")
    print("=" * 60)
    
    for i, website in enumerate(websites, 1):
        client_info = f" ({website['client']})" if 'client' in website else ""
        print(f"\n📍 Processing website {i}/{len(websites)}: {website['url']}{client_info}")
        print("-" * 40)
        
        # Initialize the login screenshot tool for each website
        login_tool = WebsiteLoginScreenshot(headless=headless)
        
        try:
            # Set default selectors if not provided
            username_selector = website.get('username_selector', '#txtUserName')
            password_selector = website.get('password_selector', '#txtPassword')
            login_button_selector = website.get('login_button_selector', '#btnLogin')
            
            print(f"🔑 Using selectors:")
            print(f"   Username: {username_selector}")
            print(f"   Password: {password_selector}")
            print(f"   Login Button: {login_button_selector}")
            
            # Perform login
            login_result = login_tool.login_to_website(
                url=website['url'],
                username=website['username'],
                password=website['password'],
                username_selector=username_selector,
                password_selector=password_selector,
                login_button_selector=login_button_selector
            )
            
            # Create result entry with detailed information
            result_entry = {
                'timestamp': datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                'client': website.get('client', ''),
                'website': website['url'],
                'connection_status': login_result.get('connection_status', 'Unknown'),
                'login_status': login_result.get('login_status', 'Unknown'),
                'response_time': login_result.get('response_time', 0),
                'page_title': login_result.get('page_title', ''),
                'final_url': login_result.get('final_url', ''),
                'elements_found': login_result.get('elements_found', {}),
                'error_message': login_result.get('error_message', ''),
                'screenshot': None,
                'username_selector': username_selector,
                'password_selector': password_selector,
                'login_button_selector': login_button_selector
            }
            
            if login_result.get('success', False):
                # Wait for the page to be completely loaded
                login_tool.wait_for_page_load()
                
                # Handle organization selection if it appears
                org_result = login_tool.handle_organization_selection(timeout=15)
                organization_page_found = org_result.get('organization_page_found', False)
                organization_selected = org_result.get('organization_selected', False)
                
                # If organization page was found but selection failed, this is an error
                if organization_page_found and not organization_selected:
                    print(f"❌ Website {i}: Organization selection failed")
                    result_entry = {
                        'timestamp': datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                        'client': website.get('client', ''),
                        'website': website['url'],
                        'connection_status': 'Connected',
                        'login_status': 'Organization Selection Failed',
                        'response_time': login_result.get('response_time', 0),
                        'page_title': login_result.get('page_title', ''),
                        'final_url': login_result.get('final_url', ''),
                        'elements_found': login_result.get('elements_found', {}),
                        'error_message': org_result.get('error_message', 'Organization selection failed'),
                        'screenshot': None,
                        'data_loaded': False,
                        'refresh_clicked': False,
                        'second_refresh_attempted': False,
                        'organization_page_found': organization_page_found,
                        'organization_selected': organization_selected,
                        'page_info': {'total_appointment_pages': 0, 'current_page': 0, 'pagination_detected': False},
                        'username_selector': username_selector,
                        'password_selector': password_selector,
                        'login_button_selector': login_button_selector
                    }
                    results.append(result_entry)
                    failed += 1
                    continue
                
                # Click the refresh button to reload the dashboard data
                refresh_clicked = login_tool.click_refresh_button(timeout=15)
                
                # Wait specifically for appointment data to load (handles "No Records Found" cases)
                data_result = login_tool.wait_for_appointment_data(timeout=60)
                data_loaded = data_result.get('data_loaded', False)
                second_refresh_attempted = data_result.get('second_refresh_attempted', False)
                
                # Count appointment pages
                print("📊 Counting appointment pages...")
                page_info = login_tool.count_appointment_pages()
                result_entry['page_info'] = page_info
                
                # Add data loading status to result
                result_entry['data_loaded'] = data_loaded
                result_entry['refresh_clicked'] = refresh_clicked
                result_entry['second_refresh_attempted'] = second_refresh_attempted
                result_entry['organization_page_found'] = organization_page_found
                result_entry['organization_selected'] = organization_selected
                
                # Take screenshot after login
                screenshot_path = login_tool.take_full_page_screenshot(url=website['url'])
                
                if screenshot_path:
                    print(f"✅ Website {i} completed successfully!")
                    print(f"📸 Screenshot: {screenshot_path}")
                    if page_info.get('total_appointment_pages', 0) > 0:
                        print(f"📄 Found {page_info['total_appointment_pages']} appointment pages")
                    if not data_loaded:
                        print(f"⚠️ Note: Data may not have fully loaded (No Records Found detected)")
                    result_entry['screenshot'] = screenshot_path
                    successful += 1
                else:
                    print(f"❌ Website {i}: Failed to take screenshot")
                    result_entry['error_message'] += " | Screenshot capture failed"
                    failed += 1
            else:
                login_status = login_result.get('login_status', 'Unknown error')
                print(f"❌ Website {i}: Login issue - {login_status}")
                
                # Still try to get page info even if login failed (in case we're on a page with some info)
                try:
                    page_info = login_tool.count_appointment_pages()
                    result_entry['page_info'] = page_info
                except:
                    result_entry['page_info'] = {'total_appointment_pages': 0, 'current_page': 0, 'pagination_detected': False}
                
                # Set default values for failed login
                result_entry['data_loaded'] = False
                result_entry['refresh_clicked'] = False
                result_entry['second_refresh_attempted'] = False
                result_entry['organization_page_found'] = False
                result_entry['organization_selected'] = False
                failed += 1
            
            results.append(result_entry)
        
        except KeyboardInterrupt:
            print(f"\n⚠️ Process interrupted by user at website {i}")
            # Add interrupted entry to results
            result_entry = {
                'timestamp': datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                'client': website.get('client', ''),
                'website': website['url'],
                'connection_status': 'Interrupted',
                'login_status': 'Interrupted',
                'response_time': 0,
                'page_title': '',
                'final_url': '',
                'elements_found': {},
                'error_message': 'Process interrupted by user',
                'screenshot': None,
                'data_loaded': False,
                'refresh_clicked': False,
                'second_refresh_attempted': False,
                'organization_page_found': False,
                'organization_selected': False,
                'page_info': {'total_appointment_pages': 0, 'current_page': 0, 'pagination_detected': False},
                'username_selector': username_selector,
                'password_selector': password_selector,
                'login_button_selector': login_button_selector
            }
            results.append(result_entry)
            break
        except Exception as e:
            print(f"❌ Website {i}: Unexpected error: {e}")
            result_entry = {
                'timestamp': datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                'client': website.get('client', ''),
                'website': website['url'],
                'connection_status': 'Error',
                'login_status': 'Error',
                'response_time': 0,
                'page_title': '',
                'final_url': '',
                'elements_found': {},
                'error_message': str(e),
                'screenshot': None,
                'data_loaded': False,
                'refresh_clicked': False,
                'second_refresh_attempted': False,
                'organization_page_found': False,
                'organization_selected': False,
                'page_info': {'total_appointment_pages': 0, 'current_page': 0, 'pagination_detected': False},
                'username_selector': username_selector,
                'password_selector': password_selector,
                'login_button_selector': login_button_selector
            }
            results.append(result_entry)
            failed += 1
        finally:
            # Always close the browser for this website
            login_tool.close()
            
            # Small delay between websites
            if i < len(websites):
                print("⏳ Waiting 2 seconds before next website...")
                time.sleep(2)
    
    # Generate CSV report
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    csv_filename = f"website_status_report_{timestamp}.csv"
    csv_path = create_csv_report(results, csv_filename)
    
    # Print final summary
    print("\n" + "=" * 60)
    print("📊 FINAL SUMMARY")
    print("=" * 60)
    print(f"✅ Successful: {successful}")
    print(f"❌ Failed: {failed}")
    print(f"📊 Total: {len(results)}")
    
    # Count by status
    connection_stats = {}
    login_stats = {}
    for result in results:
        conn_status = result.get('connection_status', 'Unknown')
        login_status = result.get('login_status', 'Unknown')
        connection_stats[conn_status] = connection_stats.get(conn_status, 0) + 1
        login_stats[login_status] = login_stats.get(login_status, 0) + 1
    
    print(f"\n🔗 Connection Status Breakdown:")
    for status, count in connection_stats.items():
        print(f"   {status}: {count}")
    
    print(f"\n🔐 Login Status Breakdown:")
    for status, count in login_stats.items():
        print(f"   {status}: {count}")
    
    print("\n📋 Detailed Results:")
    for result in results:
        login_status = result.get('login_status', 'Unknown')
        if login_status == 'Success':
            status_emoji = "✅"
        elif login_status == 'Organization Selection Failed':
            status_emoji = "🏢❌"
        else:
            status_emoji = "❌"
            
        client_info = f" ({result.get('client', '')})" if result.get('client') else ""
        print(f"{status_emoji} {result['website']}{client_info}")
        print(f"   Connection: {result.get('connection_status', 'Unknown')}")
        print(f"   Login: {login_status}")
        print(f"   Response Time: {result.get('response_time', 0)}s")
        
        # Show appointment page information
        page_info = result.get('page_info', {})
        data_loaded = result.get('data_loaded', False)
        
        if page_info.get('total_appointment_pages', 0) > 0:
            print(f"   📄 Appointment Pages: {page_info['total_appointment_pages']}")
            if page_info.get('pagination_detected', False):
                print(f"   📍 Current Page: {page_info.get('current_page', 'Unknown')}")
        elif page_info.get('pagination_detected', False):
            print(f"   📄 Pagination detected but no pages counted")
        else:
            print(f"   📄 No pagination found")
            
        # Show refresh button status
        refresh_clicked = result.get('refresh_clicked', False)
        second_refresh_attempted = result.get('second_refresh_attempted', False)
        
        if refresh_clicked:
            refresh_status = "Successfully clicked and page reloaded"
            if second_refresh_attempted:
                refresh_status += " (+ second refresh attempted)"
            print(f"   🔄 Refresh Button: {refresh_status}")
        else:
            print(f"   ⚠️ Refresh Button: Not found or failed to click")
            
        # Show organization selection status
        organization_page_found = result.get('organization_page_found', False)
        organization_selected = result.get('organization_selected', False)
        
        if organization_page_found:
            if organization_selected:
                print(f"   🏢 Organization: Selection page found and organization selected")
            else:
                print(f"   ❌ Organization: Selection page found but failed to select organization")
        else:
            print(f"   🏢 Organization: No selection page detected")
            
        # Show data loading status
        if not data_loaded:
            print(f"   ⚠️ Data Loading: May have encountered 'No Records Found'")
            
        if result.get('screenshot'):
            print(f"   📸 Screenshot: {result['screenshot']}")
        if result.get('error_message'):
            print(f"   ⚠️ Error: {result['error_message']}")
        print()
    
    print(f"🎉 All websites processed!")
    print(f"📸 Screenshots saved in: screenshots/ folder")
    if csv_path:
        print(f"📊 Detailed report saved as: {csv_path}")
    print(f"📋 Check the CSV file for complete analysis and troubleshooting information.")


def main():
    """
    Main function - Process websites from Excel file
    """
    excel_file = "Cred.xlsx"
    
    print("🌐 Website Login Screenshot Tool - Batch Mode")
    print("=" * 50)
    
    # Check if Excel file exists
    if not os.path.exists(excel_file):
        print(f"❌ Excel file not found: {excel_file}")
        print("Please make sure the Cred.xlsx file exists in the current directory.")
        print("Expected format: Client | URL | Username | Password")
        return
    
    # Ask user for headless mode
    try:
        headless_input = input("Run in headless mode? (y/N): ").strip().lower()
        headless = headless_input in ['y', 'yes']
        
        if headless:
            print("🔇 Running in headless mode (no browser window)")
        else:
            print("🖥️ Running with visible browser windows")
        
        # Process all websites
        process_multiple_websites(excel_file, headless=headless)
        
    except KeyboardInterrupt:
        print("\n⚠️ Process interrupted by user")
    except Exception as e:
        print(f"❌ Unexpected error: {e}")


if __name__ == "__main__":
    main() 