timestamp,client,website_url,connection_status,login_status,response_time_seconds,page_title,final_url,username_field_found,password_field_found,login_button_found,total_appointment_pages,current_page,pagination_detected,data_loaded_successfully,refresh_button_clicked,second_refresh_attempted,screenshot_path,error_message,username_selector,password_selector,login_button_selector
2025-07-25 12:05:55,Cdpeds,https://idcdpe.checkinasyst.com:20004/dashboard/login.aspx,Connected,Success,15.13,Dashboard,https://idcdpe.checkinasyst.com:20004/Dashboard/Default.aspx,True,True,True,1,1,<PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON>,True,True,screenshots\idcdpe_checkinasyst_com_20250725_120632.png,,#txtUserName,#txtPassword,#btnLogin
2025-07-25 12:06:47,MidwestNeuro,https://nemnss.checkinasyst.com:20006/Dashboard/Login.aspx,<PERSON>rror,Error,0.47,,,<PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON>,1,1,<PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON>,,"Message: unknown error: net::ERR_NAME_NOT_RESOLVED
  (Session info: chrome=138.0.7204.158)
Stacktrace:
	GetHandleVerifier [0x0x7ff66db8e935+77845]
	GetHandleVerifier [0x0x7ff66db8e990+77936]
	(No symbol) [0x0x7ff66d949cda]
	(No symbol) [0x0x7ff66d946a93]
	(No symbol) [0x0x7ff66d937899]
	(No symbol) [0x0x7ff66d939651]
	(No symbol) [0x0x7ff66d937bb6]
	(No symbol) [0x0x7ff66d937616]
	(No symbol) [0x0x7ff66d9372da]
	(No symbol) [0x0x7ff66d934eab]
	(No symbol) [0x0x7ff66d93572c]
	(No symbol) [0x0x7ff66d94dc8a]
	(No symbol) [0x0x7ff66d9f18be]
	(No symbol) [0x0x7ff66d9c88ca]
	(No symbol) [0x0x7ff66d9f0b07]
	(No symbol) [0x0x7ff66d9c86a3]
	(No symbol) [0x0x7ff66d991791]
	(No symbol) [0x0x7ff66d992523]
	GetHandleVerifier [0x0x7ff66de6684d+3059501]
	GetHandleVerifier [0x0x7ff66de60c0d+3035885]
	GetHandleVerifier [0x0x7ff66de80400+3164896]
	GetHandleVerifier [0x0x7ff66dba8c3e+185118]
	GetHandleVerifier [0x0x7ff66dbb054f+216111]
	GetHandleVerifier [0x0x7ff66db972e4+113092]
	GetHandleVerifier [0x0x7ff66db97499+113529]
	GetHandleVerifier [0x0x7ff66db7e298+10616]
	BaseThreadInitThunk [0x0x7fffc46f259d+29]
	RtlUserThreadStart [0x0x7fffc58caf78+40]
",#txtUserName,#txtPassword,#btnLogin
2025-07-25 12:07:06,Garden,https://gardenobgyn.checkinasyst.com:20005/Dashboard/Default.aspx,Connected,Success,10.33,Organization Selection,https://gardenobgyn.checkinasyst.com:20005/Dashboard/Init.aspx,True,True,True,65,1,True,True,True,True,screenshots\gardenobgyn_checkinasyst_com_20250725_120827.png,,#txtUserName,#txtPassword,#btnLogin
2025-07-25 12:09:12,Middle TN,https://tnment.checkinasyst.com:20001/Dashboard/Default.aspx,Connected,Success,15.51,Dashboard,https://tnment.checkinasyst.com:20001/Dashboard/Default.aspx,True,True,True,6,1,True,True,True,False,screenshots\tnment_checkinasyst_com_20250725_120924.png,,#txtUserName,#txtPassword,#btnLogin
2025-07-25 12:10:03,Pedicure,https://pedicare.checkinasyst.com:20010/Dashboard/Login.aspx,Connected,Success,10.11,Organization Selection,https://pedicare.checkinasyst.com:20010/Dashboard/Init.aspx,True,True,True,2,1,True,True,True,False,screenshots\pedicare_checkinasyst_com_20250725_121027.png,,#txtUserName,#txtPassword,#btnLogin
2025-07-25 12:11:10,Pioneer,https://mapvu.checkinasyst.com:20009/Dashboard/Login.aspx,Connected,Success,15.47,Dashboard,https://mapvu.checkinasyst.com:20009/Dashboard/Default.aspx,True,True,True,4,1,True,True,True,False,screenshots\mapvu_checkinasyst_com_20250725_121122.png,,#txtUserName,#txtPassword,#btnLogin
2025-07-25 12:12:08,UMC,https://txunmc.checkinasyst.com:20003/Dashboard/Login.aspx,Connected,Success,15.5,Dashboard,https://txunmc.checkinasyst.com:20003/Dashboard/Default.aspx,True,True,True,1,1,True,True,True,False,screenshots\txunmc_checkinasyst_com_20250725_121221.png,,#txtUserName,#txtPassword,#btnLogin
2025-07-25 12:13:02,Stevens,https://spo.checkinasyst.com:20010/Dashboard/Login.aspx,Connected,Success,14.51,Dashboard,https://spo.checkinasyst.com:20010/Dashboard/Default.aspx,True,True,True,2,1,True,True,True,True,screenshots\spo_checkinasyst_com_20250725_121337.png,,#txtUserName,#txtPassword,#btnLogin
2025-07-25 12:14:14,NTCR,https://ntcrcare.checkinasyst.com:20003/Dashboard/Login.aspx,Connected,Success,9.92,Organization Selection,https://ntcrcare.checkinasyst.com:20003/Dashboard/Init.aspx,True,True,True,2,1,True,True,True,True,screenshots\ntcrcare_checkinasyst_com_20250725_121453.png,,#txtUserName,#txtPassword,#btnLogin
2025-07-25 12:15:38,FHN,https://familyhealthnetwork.checkinasyst.com:20004/Dashboard/Login.aspx,Connected,Success,14.34,Dashboard,https://familyhealthnetwork.checkinasyst.com:20004/Dashboard/Default.aspx,True,True,True,1,1,False,False,True,True,screenshots\familyhealthnetwork_checkinasyst_com_20250725_121614.png,,#txtUserName,#txtPassword,#btnLogin
2025-07-25 12:16:46,PCHS,https://pchsweb.checkinasyst.com:20006/Dashboard/Login.aspx,Connected,Success,17.16,Dashboard,https://pchsweb.checkinasyst.com:20006/Dashboard/Default.aspx,True,True,True,10,1,True,True,True,False,screenshots\pchsweb_checkinasyst_com_20250725_121659.png,,#txtUserName,#txtPassword,#btnLogin
2025-07-25 12:17:44,Orchid,https://orchardmedcenter.checkinasyst.com:20002/Dashboard/Login.aspx,Connected,Success,15.41,Dashboard,https://orchardmedcenter.checkinasyst.com:20002/Dashboard/Default.aspx,True,True,True,1,1,True,True,True,False,screenshots\orchardmedcenter_checkinasyst_com_20250725_121756.png,,#txtUserName,#txtPassword,#btnLogin
2025-07-25 12:18:39,Wikler,https://wiklerfp.checkinasyst.com:20001/Dashboard/Login.aspx,Connected,Success,16.16,Organization Selection,https://wiklerfp.checkinasyst.com:20001/Dashboard/Init.aspx,True,True,True,1,1,True,True,True,True,screenshots\wiklerfp_checkinasyst_com_20250725_121926.png,,#txtUserName,#txtPassword,#btnLogin
2025-07-25 12:20:13,Arizona Asthma,https://azsneeze.checkinasyst.com:20006/Dashboard/Login.aspx,Connected,Success,16.6,Dashboard,https://azsneeze.checkinasyst.com:20006/Dashboard/Default.aspx,True,True,True,2,1,True,True,True,True,screenshots\azsneeze_checkinasyst_com_20250725_122047.png,,#txtUserName,#txtPassword,#btnLogin
2025-07-25 12:21:27,Neurosurgical Associates,https://nsamd.checkinasyst.com:20007/Dashboard/Login.aspx,Connected,Success,10.05,Organization Selection,https://nsamd.checkinasyst.com:20007/Dashboard/Init.aspx,True,True,True,1,1,True,True,True,True,screenshots\nsamd_checkinasyst_com_20250725_122207.png,,#txtUserName,#txtPassword,#btnLogin
2025-07-25 12:22:49,Gastro,https://gastrorockies.checkinasyst.com:20003/Dashboard/Login.aspx,Connected,Success,15.64,Dashboard,https://gastrorockies.checkinasyst.com:20003/Dashboard/Default.aspx,True,True,True,1,1,False,False,True,True,screenshots\gastrorockies_checkinasyst_com_20250725_122325.png,,#txtUserName,#txtPassword,#btnLogin
2025-07-25 12:23:57,CHCWF,https://txchcwf.checkinasyst.com:20005/Dashboard/Login.aspx,Connected,Success,16.07,Dashboard,https://txchcwf.checkinasyst.com:20005/Dashboard/Default.aspx,True,True,True,42,1,True,True,True,False,screenshots\txchcwf_checkinasyst_com_20250725_122410.png,,#txtUserName,#txtPassword,#btnLogin
2025-07-25 12:24:56,Connextcare,https://connextcare.checkinasyst.com:20009/Dashboard/Login.aspx,Connected,Success,15.48,Dashboard,https://connextcare.checkinasyst.com:20009/Dashboard/Default.aspx,True,True,True,2,1,True,True,True,False,screenshots\connextcare_checkinasyst_com_20250725_122508.png,,#txtUserName,#txtPassword,#btnLogin
2025-07-25 12:25:49,Emerald,https://emeraldcoast.checkinasyst.com:20002/Dashboard/Login.aspx,Connected,Element Not Found,0.98,502 Bad Gateway,https://emeraldcoast.checkinasyst.com:20002/Dashboard/Login.aspx,False,False,False,1,1,False,False,False,False,,Username field not found: #txtUserName,#txtUserName,#txtPassword,#btnLogin
2025-07-25 12:26:15,Florida,https://flallergy.checkinasyst.com:20001/Dashboard/Login.aspx,Connected,Success,16.28,Dashboard,https://flallergy.checkinasyst.com:20001/Dashboard/Default.aspx,True,True,True,26,1,True,True,True,False,screenshots\flallergy_checkinasyst_com_20250725_122631.png,,#txtUserName,#txtPassword,#btnLogin
